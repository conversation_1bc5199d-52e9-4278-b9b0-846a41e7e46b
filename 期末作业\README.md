# 期末作业：基于新闻文本的知识图谱构建与分析

## 项目概述

本项目是知识图谱课程的期末作业，基于第二次作业的数据分析内容，使用给定的新闻数据构建知识图谱，并进行深度分析。项目实现了从原始新闻文本到结构化知识图谱的完整流程。

## 文件结构

```
期末作业/
├── README.md                           # 项目说明文档
├── 个人报告.md                         # 详细的个人报告
├── 期末作业_知识图谱分析.ipynb          # Jupyter Notebook程序文件
├── 期末作业_完整分析.py                 # 完整的Python脚本
├── 作业数据.csv                        # 原始新闻数据
├── chinese_stopwords.txt               # 中文停用词表
└── 输出文件/
    ├── extracted_entities.json         # 提取的实体数据
    ├── relations.json                  # 抽取的关系数据
    ├── knowledge_graph.gml             # 知识图谱文件
    ├── network_statistics.json        # 网络统计信息
    ├── knowledge_graph_full.png        # 完整图网络可视化
    └── knowledge_graph_main.png        # 主要连通分量可视化
```

## 技术栈

### 核心库
- **pandas**: 数据处理和分析
- **jieba**: 中文分词和词性标注
- **networkx**: 图网络构建和分析
- **matplotlib**: 数据可视化
- **json**: 数据序列化

### 算法和方法
- **基于规则的NER**: 命名实体识别
- **共现分析**: 关系抽取
- **图论算法**: 网络分析
- **Spring布局**: 图可视化

## 主要功能

### 1. 数据预处理
- 加载CSV格式的新闻数据
- 文本清理和标准化
- 中文分词和词性标注
- 停用词过滤

### 2. 命名实体识别
- **人名识别**: 基于常见姓氏和词性标注
- **地名识别**: 基于地理后缀（省、市、县等）
- **机构识别**: 基于机构后缀（公司、部、委等）

### 3. 关系抽取
- **共现关系**: 同一新闻中出现的实体间关系
- **语义关系**: 人与地点、人与机构的特定关系
- **权重计算**: 基于共现频次确定关系强度

### 4. 知识图谱构建
- 使用NetworkX构建无向图
- 节点表示实体，边表示关系
- 边权重表示关系强度
- 支持最小权重阈值过滤

### 5. 图网络分析
- **基本统计**: 节点数、边数、连通分量
- **中心性分析**: 度中心性、介数中心性、接近中心性
- **社区发现**: 识别最大连通分量

### 6. 可视化展示
- Spring布局算法
- 节点大小反映度数
- 边宽度反映权重
- 支持中文标签显示

## 使用方法

### 方法一：运行Jupyter Notebook
```bash
jupyter notebook 期末作业_知识图谱分析.ipynb
```

### 方法二：运行Python脚本
```bash
python 期末作业_完整分析.py
```

## 环境要求

### Python版本
- Python 3.7+

### 依赖库
```bash
pip install pandas jieba networkx matplotlib
```

### 字体要求
- 系统需要安装中文字体（如SimHei、Microsoft YaHei）以正确显示中文标签

## 输出结果

### 数据文件
1. **extracted_entities.json**: 提取的实体数据，包含人名、地名、机构名
2. **relations.json**: 抽取的关系数据，包含实体对和关系类型
3. **knowledge_graph.gml**: 知识图谱文件，可用于图分析软件
4. **network_statistics.json**: 网络统计信息，包含基本指标

### 可视化文件
1. **knowledge_graph_full.png**: 完整知识图谱的可视化
2. **knowledge_graph_main.png**: 主要连通分量的可视化

## 主要发现

### 实体识别效果
- 成功识别了新闻中的主要人物、地点和机构
- 基于规则的方法在新闻领域表现良好
- 适合中文新闻文本的实体类型

### 关系网络特征
- 形成了以重要人物为中心的关系网络
- 地理位置和机构形成了语义聚类
- 权重反映了实体间的关联强度

### 图结构特点
- 存在明显的中心节点
- 形成了多个连通分量
- 体现了新闻事件的关联性

## 应用价值

### 学术价值
- 展示了知识图谱构建的完整流程
- 结合了多种NLP和图分析技术
- 为进一步研究提供了基础

### 实用价值
- 可用于新闻事件分析
- 支持人物关系挖掘
- 便于信息检索和知识发现

## 扩展方向

### 技术改进
1. **更先进的NER**: 使用深度学习模型
2. **关系分类**: 识别更细粒度的关系类型
3. **时序分析**: 考虑时间维度的动态网络
4. **多模态融合**: 结合文本、图像等多种信息

### 功能扩展
1. **交互式可视化**: 支持用户交互的图形界面
2. **实时更新**: 支持新数据的增量更新
3. **查询接口**: 提供图查询和推理功能
4. **评估指标**: 建立质量评估体系

## 注意事项

1. **数据编码**: 确保CSV文件使用GBK编码
2. **内存使用**: 大规模数据可能需要优化内存使用
3. **可视化性能**: 节点数量过多时可视化可能较慢
4. **中文支持**: 确保系统支持中文字体显示

## 联系信息

如有问题或建议，请联系：
- 作者：学生
- 课程：知识图谱
- 日期：2025年

---

**项目总结**: 本项目成功实现了从新闻文本到知识图谱的完整构建流程，展示了术语提取、实体识别、关系抽取和图网络分析的有效方法，为新闻内容的智能化分析提供了实用的解决方案。
