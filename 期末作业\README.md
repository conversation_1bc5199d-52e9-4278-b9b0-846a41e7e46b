# 期末作业：基于新闻文本的知识图谱构建与分析

## 项目概述

基于1889条中德关系新闻数据，构建知识图谱并进行网络分析。成功识别521个实体，抽取6531个关系，构建了458个节点、2961条边的大规模关系网络。

## 核心文件

```
期末作业/
├── 个人报告.md                         # 详细分析报告（基于真实运行结果）
├── 期末作业_知识图谱分析.ipynb          # Jupyter Notebook程序
├── 知识图谱分析.py                     # Python脚本（无需外部依赖）
├── 作业数据.csv                        # 1889条新闻原始数据
├── chinese_stopwords.txt               # 中文停用词表
└── 分析结果/
    ├── complete_analysis_report.json   # 完整分析报告
    ├── complete_entities.json          # 521个实体数据
    └── complete_relations.json         # 6531个关系数据
```

## 主要成果

### 实体识别结果
- **人名**: 291个（王毅、习近平、朔尔茨等关键人物）
- **地名**: 38个（德国、中国、欧洲等重要地区）
- **机构**: 192个（外交部、德中友好协会等重要机构）

### 关系网络特征
- **关系总数**: 6531个（人人关系2755个、人地关系2628个、人机构关系1148个）
- **网络规模**: 458个节点、2961条边
- **核心节点**: 德国（124度）、中国（117度）、王毅（69度）
- **网络密度**: 平均度数14.35，高度连通

### 关键发现
- 德国和中国在关系网络中具有对等的中心地位
- 王毅作为外交部长在网络中占据核心位置
- 习近平、朔尔茨等国家领导人具有重要影响力
- 网络呈现明显的中心化和小世界特征

## 快速开始

### 运行方式
```bash
# 方式1：运行Python脚本（推荐，无需安装依赖）
python 知识图谱分析.py

# 方式2：运行Jupyter Notebook
jupyter notebook 期末作业_知识图谱分析.ipynb
```

### 输出文件
运行后会生成：
- `complete_analysis_report.json` - 完整分析报告
- `complete_entities.json` - 521个实体数据
- `complete_relations.json` - 6531个关系数据

## 技术特点

- **无外部依赖**: 主要脚本只使用Python标准库
- **大规模处理**: 处理1889条新闻数据
- **中文优化**: 针对中德关系新闻优化的实体识别
- **真实结果**: 所有数据基于实际运行结果

## 应用价值

- **国际关系研究**: 分析中德关系的网络结构
- **舆情分析**: 识别关键人物和重要事件
- **政策研究**: 理解双边关系的复杂性
- **数据挖掘**: 从新闻中提取结构化知识

---

**项目总结**: 成功构建了中德关系知识图谱，发现了德国、中国的中心地位和王毅的关键作用，为理解复杂国际关系提供了数据支撑。
