# 使用说明

## 快速开始

### 方式一：运行Python脚本（推荐）
```bash
python 知识图谱分析.py
```

### 方式二：运行Jupyter Notebook
```bash
jupyter notebook 知识图谱分析.ipynb
```

## 文件说明

### 程序文件
- `知识图谱分析.py` - 主要分析程序，无需外部依赖
- `知识图谱分析.ipynb` - Jupyter Notebook版本，基于Python脚本生成

### 数据文件
- `作业数据.csv` - 1889条新闻原始数据
- `chinese_stopwords.txt` - 中文停用词表

### 输出文件
运行后会生成：
- `complete_analysis_report.json` - 完整分析报告
- `complete_entities.json` - 521个实体数据
- `complete_relations.json` - 6531个关系数据样本

### 文档文件
- `个人报告.md` - 详细的分析报告
- `README.md` - 项目概述
- `项目总结.md` - 项目完成情况

## 预期结果

运行程序后，您将看到：
- 识别521个实体（291个人名、38个地名、192个机构）
- 抽取6531个关系
- 构建458个节点、2961条边的知识图谱
- 发现德国、中国为网络中心，王毅为关键人物

## 注意事项

1. 确保`作业数据.csv`文件在同一目录下
2. Python脚本无需安装额外依赖库
3. 运行时间约1-2分钟（取决于计算机性能）
4. 生成的JSON文件可用于后续分析

## 技术特点

- **无外部依赖**：只使用Python标准库
- **真实结果**：基于实际运行的数据
- **中文优化**：针对中德关系新闻优化
- **完整流程**：从数据加载到结果保存的完整实现
