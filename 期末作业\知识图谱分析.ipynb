{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 期末作业：基于新闻文本的知识图谱构建与分析\n", "\n", "## 项目概述\n", "基于1889条中德关系新闻数据，构建知识图谱并进行网络分析。\n", "\n", "## 预期成果\n", "- 识别521个实体（291个人名、38个地名、192个机构）\n", "- 抽取6531个关系，构建458个节点的知识图谱\n", "- 发现德国、中国为网络中心，王毅为关键人物\n", "\n", "## 技术特点\n", "- 无需外部依赖库，只使用Python标准库\n", "- 针对中德关系新闻优化的实体识别\n", "- 基于共现关系的图网络构建"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\"\"\"\n", "期末作业：基于新闻文本的知识图谱构建与分析\n", "无外部依赖版本，只使用Python标准库\n", "\"\"\"\n", "\n", "import csv\n", "import re\n", "import json\n", "from collections import defaultdict, Counter\n", "\n", "print(\"导入库完成 - 使用Python标准库\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 定义核心函数"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 数据加载函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_data(filename):\n", "    \"\"\"\n", "    加载CSV数据\n", "    \n", "    Args:\n", "        filename (str): CSV文件路径\n", "        \n", "    Returns:\n", "        list: 新闻数据列表\n", "    \"\"\"\n", "    data = []\n", "    try:\n", "        with open(filename, 'r', encoding='gbk') as f:\n", "            reader = csv.Dict<PERSON><PERSON><PERSON>(f)\n", "            for row in reader:\n", "                data.append(row)\n", "        return data\n", "    except Exception as e:\n", "        print(f\"数据加载失败: {e}\")\n", "        return []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 实体识别函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_entities(text):\n", "    \"\"\"\n", "    提取命名实体\n", "    \n", "    Args:\n", "        text (str): 输入文本\n", "        \n", "    Returns:\n", "        dict: 包含不同类型实体的字典\n", "    \"\"\"\n", "    entities = {\n", "        'PERSON': [],  # 人名\n", "        'ORG': [],     # 机构名\n", "        'GPE': []      # 地名\n", "    }\n", "    \n", "    # 人名识别（基于常见姓氏和人名模式）\n", "    person_patterns = [\n", "        r'[习李王张刘陈杨赵黄周][一-龯]{1,3}',\n", "        r'王毅[一-龯]*',\n", "        r'邓洪波',\n", "        r'朔尔茨',\n", "        r'贝尔伯克',\n", "        r'默茨'\n", "    ]\n", "    \n", "    for pattern in person_patterns:\n", "        persons = re.findall(pattern, text)\n", "        entities['PERSON'].extend(persons)\n", "    \n", "    # 地名识别（更精确的模式）\n", "    place_patterns = [\n", "        r'[一-龯]*[省市县区州](?![一-龯])',\n", "        r'德国',\n", "        r'中国',\n", "        r'汉堡',\n", "        r'慕尼黑',\n", "        r'欧洲',\n", "        r'联合国'\n", "    ]\n", "    \n", "    for pattern in place_patterns:\n", "        places = re.findall(pattern, text)\n", "        entities['GPE'].extend(places)\n", "    \n", "    # 机构识别（更精确的模式）\n", "    org_patterns = [\n", "        r'[一-龯]*[公司集团部委局院校会](?![一-龯])',\n", "        r'外交部',\n", "        r'德中友好协会',\n", "        r'基民盟',\n", "        r'政治局',\n", "        r'安全会议'\n", "    ]\n", "    \n", "    for pattern in org_patterns:\n", "        orgs = re.findall(pattern, text)\n", "        entities['ORG'].extend(orgs)\n", "    \n", "    # 去重并过滤\n", "    for entity_type in entities:\n", "        entities[entity_type] = list(set([e for e in entities[entity_type] if len(e) >= 2]))\n", "    \n", "    return entities"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 关系抽取函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_relations(entity_news_mapping):\n", "    \"\"\"\n", "    抽取实体间的关系\n", "    \n", "    Args:\n", "        entity_news_mapping (list): 实体-新闻映射列表\n", "        \n", "    Returns:\n", "        list: 关系列表\n", "    \"\"\"\n", "    relations = []\n", "    \n", "    for news_item in entity_news_mapping:\n", "        entities = news_item['entities']\n", "        news_id = news_item['news_id']\n", "        \n", "        # 人与人的关系\n", "        persons = entities['PERSON']\n", "        if len(persons) >= 2:\n", "            for i in range(len(persons)):\n", "                for j in range(i+1, len(persons)):\n", "                    relations.append({\n", "                        'source': persons[i],\n", "                        'target': persons[j],\n", "                        'relation_type': 'person_person',\n", "                        'news_id': news_id\n", "                    })\n", "        \n", "        # 人与地点的关系\n", "        for person in persons:\n", "            for place in entities['GPE']:\n", "                relations.append({\n", "                    'source': person,\n", "                    'target': place,\n", "                    'relation_type': 'person_location',\n", "                    'news_id': news_id\n", "                })\n", "        \n", "        # 人与机构的关系\n", "        for person in persons:\n", "            for org in entities['ORG']:\n", "                relations.append({\n", "                    'source': person,\n", "                    'target': org,\n", "                    'relation_type': 'person_organization',\n", "                    'news_id': news_id\n", "                })\n", "    \n", "    return relations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4 图网络构建函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def build_simple_graph(relations):\n", "    \"\"\"\n", "    构建简单图结构\n", "    \n", "    Args:\n", "        relations (list): 关系列表\n", "        \n", "    Returns:\n", "        dict: 图结构字典\n", "    \"\"\"\n", "    graph = {\n", "        'nodes': set(),\n", "        'edges': defaultdict(int),\n", "        'edge_types': defaultdict(str)\n", "    }\n", "    \n", "    for relation in relations:\n", "        source = relation['source']\n", "        target = relation['target']\n", "        rel_type = relation['relation_type']\n", "        \n", "        graph['nodes'].add(source)\n", "        graph['nodes'].add(target)\n", "        \n", "        edge_key = tuple(sorted([source, target]))\n", "        graph['edges'][edge_key] += 1\n", "        graph['edge_types'][edge_key] = rel_type\n", "    \n", "    return graph"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.5 图网络分析函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_graph(graph, min_weight=2):\n", "    \"\"\"\n", "    分析图结构\n", "    \n", "    Args:\n", "        graph (dict): 图结构\n", "        min_weight (int): 最小权重阈值\n", "        \n", "    Returns:\n", "        tuple: 统计信息、过滤后的边、节点度数\n", "    \"\"\"\n", "    # 过滤边\n", "    filtered_edges = {k: v for k, v in graph['edges'].items() if v >= min_weight}\n", "    \n", "    # 计算节点度数\n", "    node_degrees = defaultdict(int)\n", "    for edge_key in filtered_edges:\n", "        node1, node2 = edge_key\n", "        node_degrees[node1] += 1\n", "        node_degrees[node2] += 1\n", "    \n", "    # 统计信息\n", "    stats = {\n", "        'total_nodes': len(graph['nodes']),\n", "        'total_edges': len(graph['edges']),\n", "        'filtered_edges': len(filtered_edges),\n", "        'filtered_nodes': len(node_degrees),\n", "        'avg_degree': sum(node_degrees.values()) / len(node_degrees) if node_degrees else 0,\n", "        'max_degree_node': max(node_degrees.items(), key=lambda x: x[1]) if node_degrees else None,\n", "        'top_nodes': sorted(node_degrees.items(), key=lambda x: x[1], reverse=True)[:10]\n", "    }\n", "    \n", "    return stats, filtered_edges, node_degrees"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 执行完整的分析流程"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 加载数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== 期末作业：新闻知识图谱分析 ===\")\n", "\n", "# 1. 加载数据\n", "print(\"\\n1. 加载数据...\")\n", "data = load_data(\"作业数据.csv\")\n", "if not data:\n", "    print(\"数据加载失败，请检查文件路径\")\n", "else:\n", "    print(f\"数据集包含 {len(data)} 条新闻\")\n", "    print(f\"处理全部 {len(data)} 条新闻\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 实体识别"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. 实体识别\n", "print(\"\\n2. 进行实体识别...\")\n", "all_entities = {\n", "    'PERSON': [],\n", "    'ORG': [],\n", "    'GPE': []\n", "}\n", "\n", "entity_news_mapping = []\n", "\n", "for i, row in enumerate(data):\n", "    title = row.get('title', '')\n", "    content = row.get('content', '')\n", "    text = title + \" \" + content\n", "    \n", "    entities = extract_entities(text)\n", "    \n", "    entity_news_mapping.append({\n", "        'news_id': i,\n", "        'entities': entities,\n", "        'text': text[:100] + \"...\" if len(text) > 100 else text\n", "    })\n", "    \n", "    # 合并到总实体列表\n", "    for entity_type in all_entities:\n", "        all_entities[entity_type].extend(entities[entity_type])\n", "\n", "# 去重\n", "for entity_type in all_entities:\n", "    all_entities[entity_type] = list(set(all_entities[entity_type]))\n", "\n", "print(f\"总计识别实体:\")\n", "print(f\"人名: {len(all_entities['PERSON'])} 个\")\n", "print(f\"地名: {len(all_entities['GPE'])} 个\") \n", "print(f\"机构: {len(all_entities['ORG'])} 个\")\n", "\n", "# 显示主要实体\n", "print(f\"\\n主要人名: {all_entities['PERSON'][:10]}\")\n", "print(f\"主要地名: {all_entities['GPE'][:10]}\")\n", "print(f\"主要机构: {all_entities['ORG'][:10]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 关系抽取"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. 关系抽取\n", "print(\"\\n3. 进行关系抽取...\")\n", "relations = extract_relations(entity_news_mapping)\n", "print(f\"共抽取到 {len(relations)} 个关系\")\n", "\n", "# 统计关系类型\n", "relation_types = Counter([r['relation_type'] for r in relations])\n", "print(\"关系类型统计：\")\n", "for rel_type, count in relation_types.items():\n", "    print(f\"  {rel_type}: {count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 图网络构建与分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. 构建图网络\n", "print(\"\\n4. 构建图网络...\")\n", "graph = build_simple_graph(relations)\n", "print(f\"图包含 {len(graph['nodes'])} 个节点和 {len(graph['edges'])} 条边\")\n", "\n", "# 5. 图网络分析\n", "print(\"\\n5. 图网络分析...\")\n", "stats, filtered_edges, node_degrees = analyze_graph(graph, min_weight=2)\n", "\n", "print(f\"过滤后图包含 {stats['filtered_nodes']} 个节点和 {stats['filtered_edges']} 条边\")\n", "print(f\"平均度数: {stats['avg_degree']:.2f}\")\n", "\n", "if stats['max_degree_node']:\n", "    print(f\"度数最高的节点: {stats['max_degree_node'][0]} (度数: {stats['max_degree_node'][1]})\")\n", "\n", "print(\"\\n度数最高的10个节点：\")\n", "for node, degree in stats['top_nodes']:\n", "    print(f\"  {node}: {degree}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.5 保存分析结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. 保存分析结果\n", "print(\"\\n6. 保存分析结果...\")\n", "\n", "# 保存完整分析报告\n", "analysis_report = {\n", "    'data_summary': {\n", "        'total_news': len(data),\n", "        'processed_news': len(data)\n", "    },\n", "    'entity_analysis': {\n", "        'total_entities': sum(len(entities) for entities in all_entities.values()),\n", "        'entity_counts': {\n", "            'PERSON': len(all_entities['PERSON']),\n", "            'ORG': len(all_entities['ORG']),\n", "            'GPE': len(all_entities['GPE'])\n", "        },\n", "        'top_entities': {\n", "            'PERSON': all_entities['PERSON'][:10],\n", "            'ORG': all_entities['ORG'][:10],\n", "            'GPE': all_entities['GPE'][:10]\n", "        }\n", "    },\n", "    'relation_analysis': {\n", "        'total_relations': len(relations),\n", "        'relation_types': dict(relation_types)\n", "    },\n", "    'graph_analysis': stats\n", "}\n", "\n", "with open('complete_analysis_report.json', 'w', encoding='utf-8') as f:\n", "    json.dump(analysis_report, f, ensure_ascii=False, indent=2)\n", "\n", "# 保存实体和关系数据\n", "with open('complete_entities.json', 'w', encoding='utf-8') as f:\n", "    json.dump(all_entities, f, ensure_ascii=False, indent=2)\n", "\n", "with open('complete_relations.json', 'w', encoding='utf-8') as f:\n", "    json.dump(relations[:100], f, ensure_ascii=False, indent=2)  # 只保存前100个关系作为示例\n", "\n", "print(\"结果已保存：\")\n", "print(\"- complete_analysis_report.json: 完整分析报告\")\n", "print(\"- complete_entities.json: 实体数据\")\n", "print(\"- complete_relations.json: 关系数据（示例）\")\n", "\n", "print(\"\\n=== 分析完成 ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 结果总结与分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== 项目总结 ===\")\n", "print(f\"1. 数据处理：成功处理了 {len(data)} 条新闻数据\")\n", "print(f\"2. 实体识别：识别出 {len(all_entities['PERSON'])} 个人名，{len(all_entities['GPE'])} 个地名，{len(all_entities['ORG'])} 个机构\")\n", "print(f\"3. 关系抽取：抽取了 {len(relations)} 个实体关系\")\n", "print(f\"4. 图谱构建：构建了包含 {len(graph['nodes'])} 个节点和 {len(graph['edges'])} 条边的知识图谱\")\n", "print(f\"5. 网络分析：过滤后包含 {stats['filtered_nodes']} 个核心节点\")\n", "\n", "print(\"\\n=== 主要发现 ===\")\n", "if stats['max_degree_node']:\n", "    print(f\"- 最重要的实体（度数最高）: {stats['max_degree_node'][0]} (度数: {stats['max_degree_node'][1]})\")\n", "    print(f\"- 平均度数: {stats['avg_degree']:.2f}\")\n", "    print(f\"- 网络特征: 高度连通的关系网络\")\n", "\n", "print(\"\\n=== 应用价值 ===\")\n", "print(\"1. 新闻内容结构化：将非结构化的新闻文本转换为结构化的知识图谱\")\n", "print(\"2. 实体关系发现：揭示了新闻中实体间的隐含关系\")\n", "print(\"3. 网络分析：识别了关键实体和重要关系\")\n", "print(\"4. 数据支持：为后续的信息检索和推荐系统提供数据基础\")\n", "\n", "print(\"\\n=== 项目完成 ===\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}