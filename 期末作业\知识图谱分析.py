#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
期末作业：基于新闻文本的知识图谱构建与分析

功能：
- 处理1889条中德关系新闻数据
- 识别521个实体（291个人名、38个地名、192个机构）
- 抽取6531个关系，构建458个节点的知识图谱
- 发现德国、中国为网络中心，王毅为关键人物

使用方法：
    python 知识图谱分析.py

输出文件：
- complete_analysis_report.json: 完整分析报告
- complete_entities.json: 实体数据
- complete_relations.json: 关系数据

注意：本脚本无需安装外部依赖库，只使用Python标准库
"""

import csv
import re
import json
from collections import defaultdict, Counter

def load_data(filename):
    """加载CSV数据"""
    data = []
    try:
        with open(filename, 'r', encoding='gbk') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)
        return data
    except Exception as e:
        print(f"数据加载失败: {e}")
        return []

def extract_entities(text):
    """提取命名实体"""
    entities = {
        'PERSON': [],
        'ORG': [],
        'GPE': []
    }
    
    # 人名识别（基于常见姓氏和人名模式）
    person_patterns = [
        r'[习李王张刘陈杨赵黄周][一-龯]{1,3}',
        r'王毅[一-龯]*',
        r'邓洪波',
        r'朔尔茨',
        r'贝尔伯克',
        r'默茨'
    ]
    
    for pattern in person_patterns:
        persons = re.findall(pattern, text)
        entities['PERSON'].extend(persons)
    
    # 地名识别（更精确的模式）
    place_patterns = [
        r'[一-龯]*[省市县区州](?![一-龯])',
        r'德国',
        r'中国',
        r'汉堡',
        r'慕尼黑',
        r'欧洲',
        r'联合国'
    ]
    
    for pattern in place_patterns:
        places = re.findall(pattern, text)
        entities['GPE'].extend(places)
    
    # 机构识别（更精确的模式）
    org_patterns = [
        r'[一-龯]*[公司集团部委局院校会](?![一-龯])',
        r'外交部',
        r'德中友好协会',
        r'基民盟',
        r'政治局',
        r'安全会议'
    ]
    
    for pattern in org_patterns:
        orgs = re.findall(pattern, text)
        entities['ORG'].extend(orgs)
    
    # 去重并过滤
    for entity_type in entities:
        entities[entity_type] = list(set([e for e in entities[entity_type] if len(e) >= 2]))
    
    return entities

def extract_relations(entity_news_mapping):
    """抽取实体间的关系"""
    relations = []
    
    for news_item in entity_news_mapping:
        entities = news_item['entities']
        news_id = news_item['news_id']
        
        # 人与人的关系
        persons = entities['PERSON']
        if len(persons) >= 2:
            for i in range(len(persons)):
                for j in range(i+1, len(persons)):
                    relations.append({
                        'source': persons[i],
                        'target': persons[j],
                        'relation_type': 'person_person',
                        'news_id': news_id
                    })
        
        # 人与地点的关系
        for person in persons:
            for place in entities['GPE']:
                relations.append({
                    'source': person,
                    'target': place,
                    'relation_type': 'person_location',
                    'news_id': news_id
                })
        
        # 人与机构的关系
        for person in persons:
            for org in entities['ORG']:
                relations.append({
                    'source': person,
                    'target': org,
                    'relation_type': 'person_organization',
                    'news_id': news_id
                })
    
    return relations

def build_simple_graph(relations):
    """构建简单图结构"""
    graph = {
        'nodes': set(),
        'edges': defaultdict(int),
        'edge_types': defaultdict(str)
    }
    
    for relation in relations:
        source = relation['source']
        target = relation['target']
        rel_type = relation['relation_type']
        
        graph['nodes'].add(source)
        graph['nodes'].add(target)
        
        edge_key = tuple(sorted([source, target]))
        graph['edges'][edge_key] += 1
        graph['edge_types'][edge_key] = rel_type
    
    return graph

def analyze_graph(graph, min_weight=2):
    """分析图结构"""
    # 过滤边
    filtered_edges = {k: v for k, v in graph['edges'].items() if v >= min_weight}
    
    # 计算节点度数
    node_degrees = defaultdict(int)
    for edge_key in filtered_edges:
        node1, node2 = edge_key
        node_degrees[node1] += 1
        node_degrees[node2] += 1
    
    # 统计信息
    stats = {
        'total_nodes': len(graph['nodes']),
        'total_edges': len(graph['edges']),
        'filtered_edges': len(filtered_edges),
        'filtered_nodes': len(node_degrees),
        'avg_degree': sum(node_degrees.values()) / len(node_degrees) if node_degrees else 0,
        'max_degree_node': max(node_degrees.items(), key=lambda x: x[1]) if node_degrees else None,
        'top_nodes': sorted(node_degrees.items(), key=lambda x: x[1], reverse=True)[:10]
    }
    
    return stats, filtered_edges, node_degrees

def main():
    """主函数"""
    print("=== 期末作业：新闻知识图谱分析（完整版） ===")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    data = load_data("作业数据.csv")
    if not data:
        return
    
    print(f"数据集包含 {len(data)} 条新闻")
    
    # 处理所有数据
    print(f"处理全部 {len(data)} 条新闻")
    
    # 2. 实体识别
    print("\n2. 进行实体识别...")
    all_entities = {
        'PERSON': [],
        'ORG': [],
        'GPE': []
    }
    
    entity_news_mapping = []
    
    for i, row in enumerate(data):
        title = row.get('title', '')
        content = row.get('content', '')
        text = title + " " + content
        
        entities = extract_entities(text)
        
        entity_news_mapping.append({
            'news_id': i,
            'entities': entities,
            'text': text[:100] + "..." if len(text) > 100 else text
        })
        
        # 合并到总实体列表
        for entity_type in all_entities:
            all_entities[entity_type].extend(entities[entity_type])
    
    # 去重
    for entity_type in all_entities:
        all_entities[entity_type] = list(set(all_entities[entity_type]))
    
    print(f"总计识别实体:")
    print(f"人名: {len(all_entities['PERSON'])} 个")
    print(f"地名: {len(all_entities['GPE'])} 个") 
    print(f"机构: {len(all_entities['ORG'])} 个")
    
    # 显示主要实体
    print(f"\n主要人名: {all_entities['PERSON'][:10]}")
    print(f"主要地名: {all_entities['GPE'][:10]}")
    print(f"主要机构: {all_entities['ORG'][:10]}")
    
    # 3. 关系抽取
    print("\n3. 进行关系抽取...")
    relations = extract_relations(entity_news_mapping)
    print(f"共抽取到 {len(relations)} 个关系")
    
    # 统计关系类型
    relation_types = Counter([r['relation_type'] for r in relations])
    print("关系类型统计：")
    for rel_type, count in relation_types.items():
        print(f"  {rel_type}: {count}")
    
    # 4. 构建图网络
    print("\n4. 构建图网络...")
    graph = build_simple_graph(relations)
    print(f"图包含 {len(graph['nodes'])} 个节点和 {len(graph['edges'])} 条边")
    
    # 5. 图网络分析
    print("\n5. 图网络分析...")
    stats, filtered_edges, node_degrees = analyze_graph(graph, min_weight=2)
    
    print(f"过滤后图包含 {stats['filtered_nodes']} 个节点和 {stats['filtered_edges']} 条边")
    print(f"平均度数: {stats['avg_degree']:.2f}")
    
    if stats['max_degree_node']:
        print(f"度数最高的节点: {stats['max_degree_node'][0]} (度数: {stats['max_degree_node'][1]})")
    
    print("\n度数最高的10个节点：")
    for node, degree in stats['top_nodes']:
        print(f"  {node}: {degree}")
    
    # 6. 保存结果
    print("\n6. 保存分析结果...")
    
    # 保存完整分析报告
    analysis_report = {
        'data_summary': {
            'total_news': len(data),
            'processed_news': len(data)
        },
        'entity_analysis': {
            'total_entities': sum(len(entities) for entities in all_entities.values()),
            'entity_counts': {
                'PERSON': len(all_entities['PERSON']),
                'ORG': len(all_entities['ORG']),
                'GPE': len(all_entities['GPE'])
            },
            'top_entities': {
                'PERSON': all_entities['PERSON'][:10],
                'ORG': all_entities['ORG'][:10],
                'GPE': all_entities['GPE'][:10]
            }
        },
        'relation_analysis': {
            'total_relations': len(relations),
            'relation_types': dict(relation_types)
        },
        'graph_analysis': stats
    }
    
    with open('complete_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_report, f, ensure_ascii=False, indent=2)
    
    # 保存实体和关系数据
    with open('complete_entities.json', 'w', encoding='utf-8') as f:
        json.dump(all_entities, f, ensure_ascii=False, indent=2)
    
    with open('complete_relations.json', 'w', encoding='utf-8') as f:
        json.dump(relations[:100], f, ensure_ascii=False, indent=2)  # 只保存前100个关系作为示例
    
    print("结果已保存：")
    print("- complete_analysis_report.json: 完整分析报告")
    print("- complete_entities.json: 实体数据")
    print("- complete_relations.json: 关系数据（示例）")
    
    print("\n=== 分析完成 ===")
    return analysis_report

if __name__ == "__main__":
    report = main()
