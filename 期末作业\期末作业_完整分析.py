#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
期末作业：基于新闻文本的知识图谱构建与分析
完整的Python实现脚本

作者：学生
日期：2025年
课程：知识图谱
"""

import pandas as pd
import jieba
import jieba.posseg as pseg
import re
import math
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from collections import defaultdict, Counter
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_stopwords(path="chinese_stopwords.txt"):
    """
    加载停用词表
    
    Args:
        path (str): 停用词文件路径
        
    Returns:
        set: 停用词集合
    """
    try:
        with open(path, "r", encoding="utf-8") as f:
            return set(line.strip() for line in f if line.strip())
    except FileNotFoundError:
        print("警告：没有找到停用词表，将继续进行分析")
        return set()

def clean_text(text):
    """
    清理文本，保留中文、数字和英文字母
    
    Args:
        text (str): 原始文本
        
    Returns:
        str: 清理后的文本
    """
    return re.sub(r"[^\u4e00-\u9fff0-9a-zA-Z]+", " ", text)

def extract_entities(text):
    """
    提取命名实体
    
    Args:
        text (str): 输入文本
        
    Returns:
        dict: 包含不同类型实体的字典
    """
    entities = {
        'PERSON': [],  # 人名
        'ORG': [],     # 机构名
        'GPE': []      # 地名
    }
    
    # 使用jieba进行词性标注
    words = pseg.cut(text)
    
    for word, flag in words:
        word = word.strip()
        if len(word) < 2:
            continue
            
        # 人名识别
        if flag == 'nr' or (flag == 'n' and re.match(r'[\u4e00-\u9fff]{2,4}', word)):
            # 常见人名模式
            if any(surname in word for surname in ['习', '李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周']):
                entities['PERSON'].append(word)
        
        # 地名识别
        if flag == 'ns' or word.endswith(('省', '市', '县', '区', '国', '州')):
            entities['GPE'].append(word)
        
        # 机构名识别
        if flag == 'nt' or word.endswith(('公司', '集团', '部', '委', '局', '院', '校', '会')):
            entities['ORG'].append(word)
    
    # 去重
    for key in entities:
        entities[key] = list(set(entities[key]))
    
    return entities

def extract_relations(entity_news_mapping):
    """
    抽取实体间的关系
    
    Args:
        entity_news_mapping (list): 实体-新闻映射列表
        
    Returns:
        list: 关系列表
    """
    relations = []
    
    for news_item in entity_news_mapping:
        entities = news_item['entities']
        news_id = news_item['news_id']
        text = news_item['text']
        
        # 人与人的关系
        persons = entities['PERSON']
        if len(persons) >= 2:
            for i in range(len(persons)):
                for j in range(i+1, len(persons)):
                    relations.append({
                        'source': persons[i],
                        'target': persons[j],
                        'relation_type': 'co_occurrence',
                        'news_id': news_id,
                        'context': text
                    })
        
        # 人与地点的关系
        for person in persons:
            for place in entities['GPE']:
                relations.append({
                    'source': person,
                    'target': place,
                    'relation_type': 'person_location',
                    'news_id': news_id,
                    'context': text
                })
        
        # 人与机构的关系
        for person in persons:
            for org in entities['ORG']:
                relations.append({
                    'source': person,
                    'target': org,
                    'relation_type': 'person_organization',
                    'news_id': news_id,
                    'context': text
                })
    
    return relations

def build_knowledge_graph(relations, min_weight=2):
    """
    构建知识图谱
    
    Args:
        relations (list): 关系列表
        min_weight (int): 最小权重阈值
        
    Returns:
        nx.Graph: 构建的图网络
    """
    G = nx.Graph()
    
    # 统计边的权重（共现次数）
    edge_weights = defaultdict(int)
    edge_types = defaultdict(str)
    
    for relation in relations:
        source = relation['source']
        target = relation['target']
        rel_type = relation['relation_type']
        
        # 创建边的键（确保一致性）
        edge_key = tuple(sorted([source, target]))
        edge_weights[edge_key] += 1
        edge_types[edge_key] = rel_type
    
    # 添加节点和边
    for edge_key, weight in edge_weights.items():
        if weight >= min_weight:  # 只保留权重大于阈值的边
            source, target = edge_key
            G.add_edge(source, target, 
                      weight=weight, 
                      relation_type=edge_types[edge_key])
    
    return G

def analyze_graph_structure(G):
    """
    分析图的结构特征
    
    Args:
        G (nx.Graph): 输入图网络
        
    Returns:
        nx.Graph: 最大连通分量子图
    """
    if G.number_of_nodes() == 0:
        print("图为空，无法分析")
        return None
    
    print("=== 图结构分析 ===")
    print(f"节点数: {G.number_of_nodes()}")
    print(f"边数: {G.number_of_edges()}")
    print(f"连通分量数: {nx.number_connected_components(G)}")
    
    # 找出最大连通分量
    largest_cc = max(nx.connected_components(G), key=len)
    largest_subgraph = G.subgraph(largest_cc)
    print(f"最大连通分量包含 {len(largest_cc)} 个节点")
    
    # 计算中心性指标
    if len(largest_cc) > 1:
        betweenness = nx.betweenness_centrality(largest_subgraph)
        closeness = nx.closeness_centrality(largest_subgraph)
        
        print("\n=== 中心性分析 ===")
        print("介数中心性最高的5个节点：")
        top_betweenness = sorted(betweenness.items(), key=lambda x: x[1], reverse=True)[:5]
        for node, score in top_betweenness:
            print(f"{node}: {score:.3f}")
        
        print("\n接近中心性最高的5个节点：")
        top_closeness = sorted(closeness.items(), key=lambda x: x[1], reverse=True)[:5]
        for node, score in top_closeness:
            print(f"{node}: {score:.3f}")
    
    return largest_subgraph

def visualize_graph(G, title="实体关系图", figsize=(15, 12), save_path=None):
    """
    可视化图网络
    
    Args:
        G (nx.Graph): 输入图网络
        title (str): 图标题
        figsize (tuple): 图形大小
        save_path (str): 保存路径
    """
    if G.number_of_nodes() == 0:
        print("图为空，无法可视化")
        return
    
    plt.figure(figsize=figsize)
    
    # 使用spring布局
    pos = nx.spring_layout(G, k=3, iterations=50)
    
    # 根据度数设置节点大小
    degrees = dict(G.degree())
    node_sizes = [degrees[node] * 300 + 100 for node in G.nodes()]
    
    # 根据权重设置边的宽度
    edge_weights = [G[u][v]['weight'] for u, v in G.edges()]
    edge_widths = [w * 0.5 for w in edge_weights]
    
    # 绘制节点
    nx.draw_networkx_nodes(G, pos, 
                          node_size=node_sizes,
                          node_color='lightblue',
                          alpha=0.7)
    
    # 绘制边
    nx.draw_networkx_edges(G, pos, 
                          width=edge_widths,
                          alpha=0.5,
                          edge_color='gray')
    
    # 绘制标签
    nx.draw_networkx_labels(G, pos, 
                           font_size=8,
                           font_weight='bold')
    
    plt.title(title, fontsize=16, fontweight='bold')
    plt.axis('off')
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图形已保存到: {save_path}")
    
    plt.show()

def save_results(all_entities, relations, G):
    """
    保存分析结果
    
    Args:
        all_entities (dict): 所有实体
        relations (list): 关系列表
        G (nx.Graph): 图网络
    """
    # 保存实体数据
    with open('extracted_entities.json', 'w', encoding='utf-8') as f:
        json.dump(all_entities, f, ensure_ascii=False, indent=2)
    
    # 保存关系数据
    relations_data = []
    for rel in relations:
        relations_data.append({
            'source': rel['source'],
            'target': rel['target'],
            'relation_type': rel['relation_type'],
            'news_id': rel['news_id']
        })
    
    with open('relations.json', 'w', encoding='utf-8') as f:
        json.dump(relations_data, f, ensure_ascii=False, indent=2)
    
    # 保存图网络
    nx.write_gml(G, 'knowledge_graph.gml')
    
    # 保存网络统计信息
    network_stats = {
        'nodes': G.number_of_nodes(),
        'edges': G.number_of_edges(),
        'connected_components': nx.number_connected_components(G),
        'average_degree': sum(dict(G.degree()).values()) / G.number_of_nodes() if G.number_of_nodes() > 0 else 0,
        'density': nx.density(G)
    }
    
    with open('network_statistics.json', 'w', encoding='utf-8') as f:
        json.dump(network_stats, f, ensure_ascii=False, indent=2)
    
    print("=== 数据保存完成 ===")
    print("已保存文件：")
    print("- extracted_entities.json: 提取的实体数据")
    print("- relations.json: 抽取的关系数据")
    print("- knowledge_graph.gml: 知识图谱文件")
    print("- network_statistics.json: 网络统计信息")

def main():
    """
    主函数：执行完整的知识图谱构建与分析流程
    """
    print("=== 期末作业：新闻知识图谱构建与分析 ===")

    # 1. 加载数据
    print("\n1. 加载数据...")
    df = pd.read_csv("作业数据.csv", encoding='gbk')
    df['title'] = df['title'].fillna("")
    df['content'] = df['content'].fillna("")
    print(f"数据集包含 {len(df)} 条新闻")

    # 加载停用词
    stopwords = load_stopwords()
    print(f"加载停用词 {len(stopwords)} 个")

    # 2. 命名实体识别
    print("\n2. 进行命名实体识别...")
    all_entities = {
        'PERSON': [],
        'ORG': [],
        'GPE': []
    }

    entity_news_mapping = []

    for idx, row in df.iterrows():
        text = row['title'] + " " + row['content']
        entities = extract_entities(text)

        entity_news_mapping.append({
            'news_id': idx,
            'entities': entities,
            'text': text[:100] + "..." if len(text) > 100 else text
        })

        for entity_type in all_entities:
            all_entities[entity_type].extend(entities[entity_type])

    # 去重并统计
    for entity_type in all_entities:
        all_entities[entity_type] = list(set(all_entities[entity_type]))

    print(f"人名: {len(all_entities['PERSON'])} 个")
    print(f"机构名: {len(all_entities['ORG'])} 个")
    print(f"地名: {len(all_entities['GPE'])} 个")

    # 显示部分实体
    print("\n部分识别的实体：")
    print("人名:", all_entities['PERSON'][:10])
    print("地名:", all_entities['GPE'][:10])
    print("机构:", all_entities['ORG'][:10])

    # 3. 关系抽取
    print("\n3. 进行关系抽取...")
    relations = extract_relations(entity_news_mapping)
    print(f"共抽取到 {len(relations)} 个关系")

    # 统计关系类型
    relation_types = Counter([r['relation_type'] for r in relations])
    print("关系类型统计：")
    for rel_type, count in relation_types.items():
        print(f"{rel_type}: {count}")

    # 4. 构建图网络
    print("\n4. 构建图网络...")
    G = build_knowledge_graph(relations, min_weight=2)
    print(f"图包含 {G.number_of_nodes()} 个节点和 {G.number_of_edges()} 条边")

    if G.number_of_nodes() > 0:
        print(f"平均度数: {sum(dict(G.degree()).values()) / G.number_of_nodes():.2f}")
        print(f"网络密度: {nx.density(G):.4f}")

        # 找出度数最高的节点
        degrees = dict(G.degree())
        top_nodes = sorted(degrees.items(), key=lambda x: x[1], reverse=True)[:10]
        print("\n度数最高的10个节点：")
        for node, degree in top_nodes:
            print(f"{node}: {degree}")

    # 5. 图网络分析和可视化
    print("\n5. 图网络分析和可视化...")
    largest_subgraph = analyze_graph_structure(G)

    # 可视化
    if G.number_of_nodes() > 0:
        visualize_graph(G, "新闻实体关系图", save_path="knowledge_graph_full.png")

        if largest_subgraph and largest_subgraph.number_of_nodes() > 1:
            visualize_graph(largest_subgraph, "最大连通分量",
                          figsize=(12, 10), save_path="knowledge_graph_main.png")

    # 6. 保存结果
    print("\n6. 保存分析结果...")
    save_results(all_entities, relations, G)

    # 7. 项目总结
    print("\n=== 项目总结 ===")
    print(f"1. 数据处理：成功处理了 {len(df)} 条新闻数据")
    print(f"2. 实体识别：识别出 {len(all_entities['PERSON'])} 个人名，{len(all_entities['GPE'])} 个地名，{len(all_entities['ORG'])} 个机构")
    print(f"3. 关系抽取：抽取了 {len(relations)} 个实体关系")
    print(f"4. 图谱构建：构建了包含 {G.number_of_nodes()} 个节点和 {G.number_of_edges()} 条边的知识图谱")
    print(f"5. 网络分析：识别了 {nx.number_connected_components(G)} 个连通分量")

    print("\n=== 主要发现 ===")
    if G.number_of_nodes() > 0:
        degrees = dict(G.degree())
        max_degree_node = max(degrees.items(), key=lambda x: x[1])
        print(f"- 最重要的实体（度数最高）: {max_degree_node[0]} (度数: {max_degree_node[1]})")
        print(f"- 平均度数: {sum(degrees.values()) / len(degrees):.2f}")
        print(f"- 网络密度: {nx.density(G):.4f}")

    print("\n=== 应用价值 ===")
    print("1. 新闻内容结构化：将非结构化的新闻文本转换为结构化的知识图谱")
    print("2. 实体关系发现：揭示了新闻中实体间的隐含关系")
    print("3. 网络分析：识别了关键实体和重要关系")
    print("4. 可视化展示：提供了直观的图形化展示")
    print("5. 数据支持：为后续的信息检索和推荐系统提供数据基础")

    print("\n=== 分析完成 ===")
    return G, largest_subgraph, all_entities, relations

if __name__ == "__main__":
    G, largest_subgraph, all_entities, relations = main()
