#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试运行脚本 - 简化版本
"""

import pandas as pd
import jieba
import jieba.posseg as pseg
import re
import networkx as nx
from collections import defaultdict, Counter
import json

def load_stopwords(path="chinese_stopwords.txt"):
    """加载停用词表"""
    try:
        with open(path, "r", encoding="utf-8") as f:
            return set(line.strip() for line in f if line.strip())
    except FileNotFoundError:
        print("警告：没有找到停用词表，将继续进行分析")
        return set()

def extract_entities(text):
    """提取命名实体"""
    entities = {
        'PERSON': [],  # 人名
        'ORG': [],     # 机构名
        'GPE': []      # 地名
    }
    
    # 使用jieba进行词性标注
    words = pseg.cut(text)
    
    for word, flag in words:
        word = word.strip()
        if len(word) < 2:
            continue
            
        # 人名识别
        if flag == 'nr' or (flag == 'n' and re.match(r'[\u4e00-\u9fff]{2,4}', word)):
            # 常见人名模式
            if any(surname in word for surname in ['习', '李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周']):
                entities['PERSON'].append(word)
        
        # 地名识别
        if flag == 'ns' or word.endswith(('省', '市', '县', '区', '国', '州')):
            entities['GPE'].append(word)
        
        # 机构名识别
        if flag == 'nt' or word.endswith(('公司', '集团', '部', '委', '局', '院', '校', '会')):
            entities['ORG'].append(word)
    
    # 去重
    for key in entities:
        entities[key] = list(set(entities[key]))
    
    return entities

def main():
    """主函数"""
    print("=== 测试运行：新闻知识图谱分析 ===")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    try:
        df = pd.read_csv("作业数据.csv", encoding='gbk')
        df['title'] = df['title'].fillna("")
        df['content'] = df['content'].fillna("")
        print(f"数据集包含 {len(df)} 条新闻")
        
        # 只处理前10条数据进行测试
        df_test = df.head(10)
        print(f"测试处理前 {len(df_test)} 条新闻")
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 2. 命名实体识别
    print("\n2. 进行命名实体识别...")
    all_entities = {
        'PERSON': [],
        'ORG': [],
        'GPE': []
    }
    
    entity_news_mapping = []
    
    for idx, row in df_test.iterrows():
        text = str(row['title']) + " " + str(row['content'])
        entities = extract_entities(text)
        
        entity_news_mapping.append({
            'news_id': idx,
            'entities': entities,
            'text': text[:100] + "..." if len(text) > 100 else text
        })
        
        for entity_type in all_entities:
            all_entities[entity_type].extend(entities[entity_type])
    
    # 去重并统计
    for entity_type in all_entities:
        all_entities[entity_type] = list(set(all_entities[entity_type]))
    
    print(f"人名: {len(all_entities['PERSON'])} 个")
    print(f"机构名: {len(all_entities['ORG'])} 个")
    print(f"地名: {len(all_entities['GPE'])} 个")
    
    # 显示部分实体
    print("\n部分识别的实体：")
    print("人名:", all_entities['PERSON'][:5])
    print("地名:", all_entities['GPE'][:5])
    print("机构:", all_entities['ORG'][:5])
    
    # 3. 简单关系抽取
    print("\n3. 进行关系抽取...")
    relations = []
    
    for news_item in entity_news_mapping:
        entities = news_item['entities']
        news_id = news_item['news_id']
        
        # 人与人的关系
        persons = entities['PERSON']
        if len(persons) >= 2:
            for i in range(len(persons)):
                for j in range(i+1, len(persons)):
                    relations.append({
                        'source': persons[i],
                        'target': persons[j],
                        'relation_type': 'co_occurrence',
                        'news_id': news_id
                    })
    
    print(f"共抽取到 {len(relations)} 个关系")
    
    # 4. 构建简单图网络
    print("\n4. 构建图网络...")
    G = nx.Graph()
    
    # 统计边的权重
    edge_weights = defaultdict(int)
    
    for relation in relations:
        source = relation['source']
        target = relation['target']
        edge_key = tuple(sorted([source, target]))
        edge_weights[edge_key] += 1
    
    # 添加边
    for edge_key, weight in edge_weights.items():
        if weight >= 1:  # 保留所有边
            source, target = edge_key
            G.add_edge(source, target, weight=weight)
    
    print(f"图包含 {G.number_of_nodes()} 个节点和 {G.number_of_edges()} 条边")
    
    # 5. 基本分析
    print("\n5. 基本分析...")
    if G.number_of_nodes() > 0:
        print(f"连通分量数: {nx.number_connected_components(G)}")
        print(f"平均度数: {sum(dict(G.degree()).values()) / G.number_of_nodes():.2f}")
        
        # 找出度数最高的节点
        degrees = dict(G.degree())
        if degrees:
            top_nodes = sorted(degrees.items(), key=lambda x: x[1], reverse=True)[:5]
            print("\n度数最高的5个节点：")
            for node, degree in top_nodes:
                print(f"{node}: {degree}")
    
    # 6. 保存结果
    print("\n6. 保存结果...")
    
    # 保存实体数据
    with open('test_entities.json', 'w', encoding='utf-8') as f:
        json.dump(all_entities, f, ensure_ascii=False, indent=2)
    
    # 保存关系数据
    with open('test_relations.json', 'w', encoding='utf-8') as f:
        json.dump(relations, f, ensure_ascii=False, indent=2)
    
    # 保存图网络
    if G.number_of_nodes() > 0:
        nx.write_gml(G, 'test_graph.gml')
    
    print("测试结果已保存：")
    print("- test_entities.json: 实体数据")
    print("- test_relations.json: 关系数据")
    print("- test_graph.gml: 图网络数据")
    
    print("\n=== 测试完成 ===")
    return G, all_entities, relations

if __name__ == "__main__":
    G, all_entities, relations = main()
