#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 不依赖外部库
"""

import csv
import re
import json

def load_data(filename):
    """加载CSV数据"""
    data = []
    try:
        with open(filename, 'r', encoding='gbk') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)
        return data
    except Exception as e:
        print(f"数据加载失败: {e}")
        return []

def simple_entity_extraction(text):
    """简单的实体提取"""
    entities = {
        'PERSON': [],
        'ORG': [],
        'GPE': []
    }
    
    # 简单的人名识别（基于常见姓氏）
    person_pattern = r'[习李王张刘陈杨赵黄周][一-龯]{1,3}'
    persons = re.findall(person_pattern, text)
    entities['PERSON'] = list(set(persons))
    
    # 简单的地名识别
    place_pattern = r'[一-龯]+[省市县区国州]'
    places = re.findall(place_pattern, text)
    entities['GPE'] = list(set(places))
    
    # 简单的机构识别
    org_pattern = r'[一-龯]+[公司集团部委局院校会]'
    orgs = re.findall(org_pattern, text)
    entities['ORG'] = list(set(orgs))
    
    return entities

def main():
    """主函数"""
    print("=== 简单测试：新闻知识图谱分析 ===")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    data = load_data("作业数据.csv")
    if not data:
        print("数据加载失败，退出程序")
        return
    
    print(f"数据集包含 {len(data)} 条新闻")
    
    # 只处理前5条数据进行测试
    test_data = data[:5]
    print(f"测试处理前 {len(test_data)} 条新闻")
    
    # 2. 实体识别
    print("\n2. 进行实体识别...")
    all_entities = {
        'PERSON': [],
        'ORG': [],
        'GPE': []
    }
    
    for i, row in enumerate(test_data):
        title = row.get('title', '')
        content = row.get('content', '')
        text = title + " " + content
        
        entities = simple_entity_extraction(text)
        
        print(f"\n新闻 {i+1}:")
        print(f"标题: {title[:50]}...")
        print(f"人名: {entities['PERSON']}")
        print(f"地名: {entities['GPE']}")
        print(f"机构: {entities['ORG']}")
        
        # 合并到总实体列表
        for entity_type in all_entities:
            all_entities[entity_type].extend(entities[entity_type])
    
    # 去重
    for entity_type in all_entities:
        all_entities[entity_type] = list(set(all_entities[entity_type]))
    
    print(f"\n总计识别实体:")
    print(f"人名: {len(all_entities['PERSON'])} 个 - {all_entities['PERSON']}")
    print(f"地名: {len(all_entities['GPE'])} 个 - {all_entities['GPE']}")
    print(f"机构: {len(all_entities['ORG'])} 个 - {all_entities['ORG']}")
    
    # 3. 简单关系统计
    print("\n3. 关系统计...")
    total_entities = sum(len(entities) for entities in all_entities.values())
    print(f"总实体数: {total_entities}")
    
    # 计算可能的关系数量
    person_count = len(all_entities['PERSON'])
    if person_count >= 2:
        person_relations = person_count * (person_count - 1) // 2
        print(f"人与人可能关系数: {person_relations}")
    
    # 4. 保存结果
    print("\n4. 保存结果...")
    
    # 保存实体数据
    with open('simple_entities.json', 'w', encoding='utf-8') as f:
        json.dump(all_entities, f, ensure_ascii=False, indent=2)
    
    # 创建简单的统计报告
    report = {
        'total_news': len(data),
        'processed_news': len(test_data),
        'entities_count': {
            'PERSON': len(all_entities['PERSON']),
            'ORG': len(all_entities['ORG']),
            'GPE': len(all_entities['GPE'])
        },
        'entities': all_entities
    }
    
    with open('simple_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("结果已保存：")
    print("- simple_entities.json: 实体数据")
    print("- simple_report.json: 分析报告")
    
    print("\n=== 测试完成 ===")
    print("这是一个简化版本的测试，完整功能请安装所需依赖库后运行完整脚本")
    
    return all_entities

if __name__ == "__main__":
    all_entities = main()
