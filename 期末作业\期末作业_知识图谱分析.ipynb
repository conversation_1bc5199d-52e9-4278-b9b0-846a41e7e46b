{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 期末作业：基于新闻文本的知识图谱构建与分析\n",
    "\n",
    "## 项目概述\n",
    "本项目基于第二次作业的数据分析内容，使用给定的新闻数据构建知识图谱，并进行深度分析。\n",
    "\n",
    "## 主要功能\n",
    "1. 数据预处理\n",
    "2. 命名实体识别\n",
    "3. 关系抽取\n",
    "4. 知识图谱构建\n",
    "5. 图网络分析\n",
    "6. 可视化展示"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "#!/usr/bin/env python\n",
    "# -*- coding: utf-8 -*-\n",
    "\"\"\"\n",
    "期末作业：新闻知识图谱构建与分析\n",
    "基于NC-value算法和图网络分析的完整实现\n",
    "\"\"\"\n",
    "\n",
    "import pandas as pd\n",
    "import jieba\n",
    "import jieba.posseg as pseg\n",
    "import re\n",
    "import math\n",
    "import networkx as nx\n",
    "import matplotlib.pyplot as plt\n",
    "import matplotlib.font_manager as fm\n",
    "from collections import defaultdict, Counter\n",
    "import json\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# 设置中文字体\n",
    "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n",
    "plt.rcParams['axes.unicode_minus'] = False\n",
    "\n",
    "print(\"导入库完成\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. 数据加载与预处理"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def load_stopwords(path=\"chinese_stopwords.txt\"):\n",
    "    \"\"\"加载停用词表\"\"\"\n",
    "    try:\n",
    "        with open(path, \"r\", encoding=\"utf-8\") as f:\n",
    "            return set(line.strip() for line in f if line.strip())\n",
    "    except FileNotFoundError:\n",
    "        print(\"警告：没有找到停用词表，将继续进行分析\")\n",
    "        return set()\n",
    "\n",
    "def clean_text(text):\n",
    "    \"\"\"清理文本，保留中文、数字和英文字母\"\"\"\n",
    "    return re.sub(r\"[^\\u4e00-\\u9fff0-9a-zA-Z]+\", \" \", text)\n",
    "\n",
    "# 加载数据\n",
    "print(\"=== 加载数据 ===\")\n",
    "df = pd.read_csv(\"作业数据.csv\", encoding='gbk')\n",
    "df['title'] = df['title'].fillna(\"\")\n",
    "df['content'] = df['content'].fillna(\"\")\n",
    "print(f\"数据集包含 {len(df)} 条新闻\")\n",
    "\n",
    "# 加载停用词\n",
    "stopwords = load_stopwords()\n",
    "print(f\"加载停用词 {len(stopwords)} 个\")\n",
    "\n",
    "# 显示数据样例\n",
    "print(\"\\n数据样例：\")\n",
    "print(df.head())"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. 命名实体识别"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def extract_entities(text):\n",
    "    \"\"\"提取命名实体\"\"\"\n",
    "    entities = {\n",
    "        'PERSON': [],  # 人名\n",
    "        'ORG': [],     # 机构名\n",
    "        'GPE': []      # 地名\n",
    "    }\n",
    "    \n",
    "    # 使用jieba进行词性标注\n",
    "    words = pseg.cut(text)\n",
    "    \n",
    "    for word, flag in words:\n",
    "        word = word.strip()\n",
    "        if len(word) < 2:\n",
    "            continue\n",
    "            \n",
    "        # 人名识别\n",
    "        if flag == 'nr' or (flag == 'n' and re.match(r'[\\u4e00-\\u9fff]{2,4}', word)):\n",
    "            # 常见人名模式\n",
    "            if any(surname in word for surname in ['习', '李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周']):\n",
    "                entities['PERSON'].append(word)\n",
    "        \n",
    "        # 地名识别\n",
    "        if flag == 'ns' or word.endswith(('省', '市', '县', '区', '国', '州')):\n",
    "            entities['GPE'].append(word)\n",
    "        \n",
    "        # 机构名识别\n",
    "        if flag == 'nt' or word.endswith(('公司', '集团', '部', '委', '局', '院', '校', '会')):\n",
    "            entities['ORG'].append(word)\n",
    "    \n",
    "    # 去重\n",
    "    for key in entities:\n",
    "        entities[key] = list(set(entities[key]))\n",
    "    \n",
    "    return entities\n",
    "\n",
    "# 进行命名实体识别\n",
    "print(\"=== 进行命名实体识别 ===\")\n",
    "all_entities = {\n",
    "    'PERSON': [],\n",
    "    'ORG': [],\n",
    "    'GPE': []\n",
    "}\n",
    "\n",
    "entity_news_mapping = []\n",
    "\n",
    "for idx, row in df.iterrows():\n",
    "    text = row['title'] + \" \" + row['content']\n",
    "    entities = extract_entities(text)\n",
    "    \n",
    "    entity_news_mapping.append({\n",
    "        'news_id': idx,\n",
    "        'entities': entities,\n",
    "        'text': text[:100] + \"...\" if len(text) > 100 else text\n",
    "    })\n",
    "    \n",
    "    for entity_type in all_entities:\n",
    "        all_entities[entity_type].extend(entities[entity_type])\n",
    "\n",
    "# 去重并统计\n",
    "for entity_type in all_entities:\n",
    "    all_entities[entity_type] = list(set(all_entities[entity_type]))\n",
    "\n",
    "print(f\"人名: {len(all_entities['PERSON'])} 个\")\n",
    "print(f\"机构名: {len(all_entities['ORG'])} 个\")\n",
    "print(f\"地名: {len(all_entities['GPE'])} 个\")\n",
    "\n",
    "# 显示部分实体\n",
    "print(\"\\n部分识别的实体：\")\n",
    "print(\"人名:\", all_entities['PERSON'][:10])\n",
    "print(\"地名:\", all_entities['GPE'][:10])\n",
    "print(\"机构:\", all_entities['ORG'][:10])"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. 关系抽取"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def extract_relations(entity_news_mapping):\n",
    "    \"\"\"抽取实体间的关系\"\"\"\n",
    "    relations = []\n",
    "    \n",
    "    for news_item in entity_news_mapping:\n",
    "        entities = news_item['entities']\n",
    "        news_id = news_item['news_id']\n",
    "        text = news_item['text']\n",
    "        \n",
    "        # 人与人的关系\n",
    "        persons = entities['PERSON']\n",
    "        if len(persons) >= 2:\n",
    "            for i in range(len(persons)):\n",
    "                for j in range(i+1, len(persons)):\n",
    "                    relations.append({\n",
    "                        'source': persons[i],\n",
    "                        'target': persons[j],\n",
    "                        'relation_type': 'co_occurrence',\n",
    "                        'news_id': news_id,\n",
    "                        'context': text\n",
    "                    })\n",
    "        \n",
    "        # 人与地点的关系\n",
    "        for person in persons:\n",
    "            for place in entities['GPE']:\n",
    "                relations.append({\n",
    "                    'source': person,\n",
    "                    'target': place,\n",
    "                    'relation_type': 'person_location',\n",
    "                    'news_id': news_id,\n",
    "                    'context': text\n",
    "                })\n",
    "        \n",
    "        # 人与机构的关系\n",
    "        for person in persons:\n",
    "            for org in entities['ORG']:\n",
    "                relations.append({\n",
    "                    'source': person,\n",
    "                    'target': org,\n",
    "                    'relation_type': 'person_organization',\n",
    "                    'news_id': news_id,\n",
    "                    'context': text\n",
    "                })\n",
    "    \n",
    "    return relations\n",
    "\n",
    "# 进行关系抽取\n",
    "print(\"=== 进行关系抽取 ===\")\n",
    "relations = extract_relations(entity_news_mapping)\n",
    "print(f\"共抽取到 {len(relations)} 个关系\")\n",
    "\n",
    "# 统计关系类型\n",
    "relation_types = Counter([r['relation_type'] for r in relations])\n",
    "print(\"关系类型统计：\")\n",
    "for rel_type, count in relation_types.items():\n",
    "    print(f\"{rel_type}: {count}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. 知识图谱构建"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def build_knowledge_graph(relations, min_weight=2):\n",
    "    \"\"\"构建知识图谱\"\"\"\n",
    "    G = nx.Graph()\n",
    "    \n",
    "    # 统计边的权重（共现次数）\n",
    "    edge_weights = defaultdict(int)\n",
    "    edge_types = defaultdict(str)\n",
    "    \n",
    "    for relation in relations:\n",
    "        source = relation['source']\n",
    "        target = relation['target']\n",
    "        rel_type = relation['relation_type']\n",
    "        \n",
    "        # 创建边的键（确保一致性）\n",
    "        edge_key = tuple(sorted([source, target]))\n",
    "        edge_weights[edge_key] += 1\n",
    "        edge_types[edge_key] = rel_type\n",
    "    \n",
    "    # 添加节点和边\n",
    "    for edge_key, weight in edge_weights.items():\n",
    "        if weight >= min_weight:  # 只保留权重大于阈值的边\n",
    "            source, target = edge_key\n",
    "            G.add_edge(source, target, \n",
    "                      weight=weight, \n",
    "                      relation_type=edge_types[edge_key])\n",
    "    \n",
    "    return G\n",
    "\n",
    "# 构建图网络\n",
    "print(\"=== 构建图网络 ===\")\n",
    "G = build_knowledge_graph(relations, min_weight=2)\n",
    "print(f\"图包含 {G.number_of_nodes()} 个节点和 {G.number_of_edges()} 条边\")\n",
    "\n",
    "if G.number_of_nodes() > 0:\n",
    "    print(f\"平均度数: {sum(dict(G.degree()).values()) / G.number_of_nodes():.2f}\")\n",
    "    \n",
    "    # 找出度数最高的节点\n",
    "    degrees = dict(G.degree())\n",
    "    top_nodes = sorted(degrees.items(), key=lambda x: x[1], reverse=True)[:10]\n",
    "    print(\"\\n度数最高的10个节点：\")\n",
    "    for node, degree in top_nodes:\n",
    "        print(f\"{node}: {degree}\")
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. 图网络分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def analyze_graph_structure(G):\n",
    "    \"\"\"分析图的结构特征\"\"\"\n",
    "    if G.number_of_nodes() == 0:\n",
    "        print(\"图为空，无法分析\")\n",
    "        return None\n",
    "    \n",
    "    print(\"=== 图结构分析 ===\")\n",
    "    print(f\"节点数: {G.number_of_nodes()}\")\n",
    "    print(f\"边数: {G.number_of_edges()}\")\n",
    "    print(f\"连通分量数: {nx.number_connected_components(G)}\")\n",
    "    \n",
    "    # 找出最大连通分量\n",
    "    largest_cc = max(nx.connected_components(G), key=len)\n",
    "    largest_subgraph = G.subgraph(largest_cc)\n",
    "    print(f\"最大连通分量包含 {len(largest_cc)} 个节点\")\n",
    "    \n",
    "    # 计算中心性指标\n",
    "    if len(largest_cc) > 1:\n",
    "        betweenness = nx.betweenness_centrality(largest_subgraph)\n",
    "        closeness = nx.closeness_centrality(largest_subgraph)\n",
    "        \n",
    "        print(\"\\n=== 中心性分析 ===\")\n",
    "        print(\"介数中心性最高的5个节点：\")\n",
    "        top_betweenness = sorted(betweenness.items(), key=lambda x: x[1], reverse=True)[:5]\n",
    "        for node, score in top_betweenness:\n",
    "            print(f\"{node}: {score:.3f}\")\n",
    "        \n",
    "        print(\"\\n接近中心性最高的5个节点：\")\n",
    "        top_closeness = sorted(closeness.items(), key=lambda x: x[1], reverse=True)[:5]\n",
    "        for node, score in top_closeness:\n",
    "            print(f\"{node}: {score:.3f}\")\n",
    "    \n",
    "    return largest_subgraph\n",
    "\n",
    "# 进行图网络分析\n",
    "largest_subgraph = analyze_graph_structure(G)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. 可视化展示"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def visualize_graph(G, title=\"实体关系图\", figsize=(15, 12)):\n",
    "    \"\"\"可视化图网络\"\"\"\n",
    "    if G.number_of_nodes() == 0:\n",
    "        print(\"图为空，无法可视化\")\n",
    "        return\n",
    "    \n",
    "    plt.figure(figsize=figsize)\n",
    "    \n",
    "    # 使用spring布局\n",
    "    pos = nx.spring_layout(G, k=3, iterations=50)\n",
    "    \n",
    "    # 根据度数设置节点大小\n",
    "    degrees = dict(G.degree())\n",
    "    node_sizes = [degrees[node] * 300 + 100 for node in G.nodes()]\n",
    "    \n",
    "    # 根据权重设置边的宽度\n",
    "    edge_weights = [G[u][v]['weight'] for u, v in G.edges()]\n",
    "    edge_widths = [w * 0.5 for w in edge_weights]\n",
    "    \n",
    "    # 绘制节点\n",
    "    nx.draw_networkx_nodes(G, pos, \n",
    "                          node_size=node_sizes,\n",
    "                          node_color='lightblue',\n",
    "                          alpha=0.7)\n",
    "    \n",
    "    # 绘制边\n",
    "    nx.draw_networkx_edges(G, pos, \n",
    "                          width=edge_widths,\n",
    "                          alpha=0.5,\n",
    "                          edge_color='gray')\n",
    "    \n",
    "    # 绘制标签\n",
    "    nx.draw_networkx_labels(G, pos, \n",
    "                           font_size=8,\n",
    "                           font_weight='bold')\n",
    "    \n",
    "    plt.title(title, fontsize=16, fontweight='bold')\n",
    "    plt.axis('off')\n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "\n",
    "# 可视化完整图网络\n",
    "if G.number_of_nodes() > 0:\n",
    "    print(\"=== 可视化图网络 ===\")\n",
    "    visualize_graph(G, \"新闻实体关系图\")\n",
    "    \n",
    "    if largest_subgraph and largest_subgraph.number_of_nodes() > 1:\n",
    "        visualize_graph(largest_subgraph, \"最大连通分量\", figsize=(12, 10))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. 数据保存与导出"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 保存实体数据\n",
    "with open('extracted_entities.json', 'w', encoding='utf-8') as f:\n",
    "    json.dump(all_entities, f, ensure_ascii=False, indent=2)\n",
    "\n",
    "# 保存关系数据\n",
    "relations_data = []\n",
    "for rel in relations:\n",
    "    relations_data.append({\n",
    "        'source': rel['source'],\n",
    "        'target': rel['target'],\n",
    "        'relation_type': rel['relation_type'],\n",
    "        'news_id': rel['news_id']\n",
    "    })\n",
    "\n",
    "with open('relations.json', 'w', encoding='utf-8') as f:\n",
    "    json.dump(relations_data, f, ensure_ascii=False, indent=2)\n",
    "\n",
    "# 保存图网络\n",
    "nx.write_gml(G, 'knowledge_graph.gml')\n",
    "\n",
    "# 保存网络统计信息\n",
    "network_stats = {\n",
    "    'nodes': G.number_of_nodes(),\n",
    "    'edges': G.number_of_edges(),\n",
    "    'connected_components': nx.number_connected_components(G),\n",
    "    'average_degree': sum(dict(G.degree()).values()) / G.number_of_nodes() if G.number_of_nodes() > 0 else 0\n",
    "}\n",
    "\n",
    "with open('network_statistics.json', 'w', encoding='utf-8') as f:\n",
    "    json.dump(network_stats, f, ensure_ascii=False, indent=2)\n",
    "\n",
    "print(\"=== 数据保存完成 ===\")\n",
    "print(\"已保存文件：\")\n",
    "print(\"- extracted_entities.json: 提取的实体数据\")\n",
    "print(\"- relations.json: 抽取的关系数据\")\n",
    "print(\"- knowledge_graph.gml: 知识图谱文件\")\n",
    "print(\"- network_statistics.json: 网络统计信息\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. 结果总结与分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "print(\"=== 项目总结 ===\")\n",
    "print(f\"1. 数据处理：成功处理了 {len(df)} 条新闻数据\")\n",
    "print(f\"2. 实体识别：识别出 {len(all_entities['PERSON'])} 个人名，{len(all_entities['GPE'])} 个地名，{len(all_entities['ORG'])} 个机构\")\n",
    "print(f\"3. 关系抽取：抽取了 {len(relations)} 个实体关系\")\n",
    "print(f\"4. 图谱构建：构建了包含 {G.number_of_nodes()} 个节点和 {G.number_of_edges()} 条边的知识图谱\")\n",
    "print(f\"5. 网络分析：识别了 {nx.number_connected_components(G)} 个连通分量\")\n",
    "\n",
    "print(\"\\n=== 主要发现 ===\")\n",
    "if G.number_of_nodes() > 0:\n",
    "    degrees = dict(G.degree())\n",
    "    max_degree_node = max(degrees.items(), key=lambda x: x[1])\n",
    "    print(f\"- 最重要的实体（度数最高）: {max_degree_node[0]} (度数: {max_degree_node[1]})\")\n",
    "    print(f\"- 平均度数: {sum(degrees.values()) / len(degrees):.2f}\")\n",
    "    print(f\"- 网络密度: {nx.density(G):.4f}\")\n",
    "\n",
    "print(\"\\n=== 应用价值 ===\")\n",
    "print(\"1. 新闻内容结构化：将非结构化的新闻文本转换为结构化的知识图谱\")\n",
    "print(\"2. 实体关系发现：揭示了新闻中实体间的隐含关系\")\n",
    "print(\"3. 网络分析：识别了关键实体和重要关系\")\n",
    "print(\"4. 可视化展示：提供了直观的图形化展示\")\n",
    "print(\"5. 数据支持：为后续的信息检索和推荐系统提供数据基础\")\n",
    "\n",
    "print(\"\\n=== 项目完成 ===\")"
   ]
  }"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
