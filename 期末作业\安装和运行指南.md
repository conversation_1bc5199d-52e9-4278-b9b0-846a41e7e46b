# 安装和运行指南

## 环境要求

- Python 3.7 或更高版本
- Windows/Linux/macOS 操作系统

## 安装步骤

### 1. 安装Python依赖库

在命令行中运行以下命令：

```bash
pip install -r requirements.txt
```

或者单独安装每个库：

```bash
pip install pandas jieba networkx matplotlib numpy
```

### 2. 验证安装

运行简单测试脚本验证环境：

```bash
python 简单测试.py
```

如果看到输出结果，说明基本环境正常。

## 运行方式

### 方式一：运行Jupyter Notebook（推荐）

1. 安装Jupyter：
```bash
pip install jupyter
```

2. 启动Jupyter Notebook：
```bash
jupyter notebook
```

3. 在浏览器中打开 `期末作业_知识图谱分析.ipynb`

4. 按顺序运行每个单元格

### 方式二：运行Python脚本

```bash
python 期末作业_完整分析.py
```

### 方式三：简化测试（无需额外依赖）

```bash
python 简单测试.py
```

## 输出文件说明

运行完成后会生成以下文件：

### 数据文件
- `extracted_entities.json`: 提取的实体数据
- `relations.json`: 抽取的关系数据
- `knowledge_graph.gml`: 知识图谱文件
- `network_statistics.json`: 网络统计信息

### 可视化文件
- `knowledge_graph_full.png`: 完整图网络可视化
- `knowledge_graph_main.png`: 主要连通分量可视化

### 测试文件（简化版本）
- `simple_entities.json`: 简化实体数据
- `simple_report.json`: 简化分析报告

## 常见问题

### 1. 中文显示问题

如果图形中中文显示为方块，请确保系统安装了中文字体：
- Windows: 通常已包含SimHei字体
- Linux: 安装中文字体包
- macOS: 系统自带中文字体

### 2. 内存不足

如果处理大量数据时内存不足：
- 减少处理的数据量
- 调整 `min_weight` 参数过滤边
- 使用更强大的硬件

### 3. 依赖库安装失败

如果pip安装失败：
- 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pandas`
- 更新pip：`python -m pip install --upgrade pip`
- 使用conda：`conda install pandas jieba networkx matplotlib`

### 4. 编码问题

如果遇到编码错误：
- 确保CSV文件使用GBK编码
- 检查系统默认编码设置

## 性能优化建议

1. **数据量控制**: 对于大数据集，可以先处理部分数据进行测试
2. **参数调整**: 调整 `min_weight` 参数来控制图的复杂度
3. **可视化优化**: 对于大图，可以只可视化主要连通分量

## 技术支持

如果遇到问题：
1. 检查Python版本是否符合要求
2. 确认所有依赖库已正确安装
3. 查看错误信息并对照常见问题
4. 可以先运行简化测试版本验证基本功能

## 项目结构

```
期末作业/
├── README.md                           # 项目说明
├── 个人报告.md                         # 详细报告
├── 安装和运行指南.md                   # 本文件
├── requirements.txt                    # 依赖库列表
├── 期末作业_知识图谱分析.ipynb          # Jupyter Notebook
├── 期末作业_完整分析.py                 # 完整Python脚本
├── 简单测试.py                         # 简化测试脚本
├── 作业数据.csv                        # 原始数据
├── chinese_stopwords.txt               # 停用词表
└── 输出文件/                           # 运行后生成的结果文件
```

祝您使用愉快！
